#include "es_inc.h"

#if (ES_4GLTE_ENABLE) && (ES_4GLTE_MODULE_TYPE == ES_4GLTE_MODULE_EC800X)

#define HAL_LTE_DEBUG
#ifdef HAL_LTE_DEBUG
#define hal_lte_debug es_log_info
#define hal_lte_error es_log_error
#else
#define hal_lte_debug(...)
#define hal_lte_error(...)
#endif

#define LTE_AT_CMD_TX_LEN                   (128)
#define LTE_AT_DATA_TX_LEN                  (2048)
#define LTE_AT_LIST_COUNT                   (4)
#define LTE_AT_RESP_READLINE_LEN            (128)
#define LTE_UART_RX_BUF_SIZE                (4096)
#define LTE_WAIT_RESP_SLEEP_TIME_MS         (10)
#if ES_GPS_ENABLE
#define LTE_GPS_UPDATE_TIME_SEC             (60)
#endif

#define LTE_SEND_CMD_WAIT_RESP(cmd_str, resp_flag, timeout_ms) do { \
        hal_lte_reset_send_param(resp_flag, timeout_ms); \
        hal_lte_exec_cmd(cmd_str); \
        PT_SEM_INIT(&sem_lte_at_resp, 0); \
        PT_SEM_WAIT(pt, &sem_lte_at_resp); \
        if (0 == (resp_flag & lte_now_at_resp)) { \
            hal_lte_debug("no resp or recv err"); \
            goto END; \
        } \
    } while (0)


#define LTE_SEND_DATA_WAIT_RESP(data, data_len, resp_flag, timeout_ms) do { \
        hal_lte_reset_send_param(resp_flag, timeout_ms); \
        hal_lte_uart_send(data, data_len); \
        PT_SEM_INIT(&sem_lte_at_resp, 0); \
        PT_SEM_WAIT(pt, &sem_lte_at_resp); \
        if (0 == (resp_flag & lte_now_at_resp)) { \
            hal_lte_debug("no resp or recv err"); \
            goto END; \
        } \
    } while (0)


#define LTE_SEND_CMD_WAIT_RESP_WITHOUT_GOEND(cmd_str, resp_flag, timeout_ms) do { \
        hal_lte_reset_send_param(resp_flag, timeout_ms); \
        hal_lte_exec_cmd(cmd_str); \
        PT_SEM_INIT(&sem_lte_at_resp, 0); \
        PT_SEM_WAIT(pt, &sem_lte_at_resp); \
    } while (0)

typedef enum {
    HAL_LTE_STATUS_TRY_RESET,
    HAL_LTE_STATUS_POWER_ON,
    HAL_LTE_STATUS_SYS_INIT,
    HAL_LTE_STATUS_NET_REG,
    HAL_LTE_STATUS_MQTT_CONN,
    HAL_LTE_STATUS_NET_READY,
    HAL_LTE_STATUS_UPDATE_GPS
} hal_lte_status_e;


typedef enum {
    AT_LTE_RESP_NONE        = 0x00,
    AT_LTE_RESP_OK          = 0x01,    // OK
    AT_LTE_RESP_CSQ         = 0x02,    // +CSQ
    AT_LTE_RESP_IPR         = 0x04,    // +IPR
    AT_LTE_RESP_CPIN        = 0x08,    // +CPIN
    AT_LTE_RESP_CGREG       = 0x10,    // +CGREG or +CEREG
    AT_LTE_RESP_HTTP_SEND   = 0x20,    // CONNECT
    AT_LTE_RESP_HTTP_GET    = 0x40,    // +QHTTPGET: 0,200
    AT_LTE_RESP_HTTP_FREAD  = 0x80,    // +QHTTPREADFILE: 0
    AT_LTE_RESP_FILE_OPEN   = 0x100,   // +QFOPEN: 1
    AT_LTE_RESP_SEND_FLAG   = 0x200,   // resp:>\r\n
    AT_LTE_RESP_SEND_OK     = 0x400,   // resp:SEND OK
    AT_LTE_RESP_SEND_FAIL   = 0x800,   // resp:SEND FAIL
    AT_LTE_RESP_GSN         = 0x1000,  // resp:+GSN
    AT_LTE_RESP_CGPADDR     = 0x2000,  // resp:+CGPADDR: 
    //0x4000,  // resp:
    AT_LTE_RESP_CELLID      = 0x8000,  // resp:+CREG: 2,1,"3747","A23C2",100
    AT_LTE_RESP_ICCID       = 0x10000, // resp:+QCCID: 89860025128306012474
    AT_LTE_RESP_MQTOPEN     = 0x20000, // resp:+MIPSTART: SUCCESS
    AT_LTE_RESP_MQTCONN     = 0x40000, // resp:+QMTCONN: 0,0,0
    AT_LTE_RESP_MQTSUB      = 0x80000, // +QMTSUB: 0,1,0,0
    AT_LTE_RESP_FILE_READ   = 0x200000, // CONNECT 1024
    AT_LTE_RESP_HTTP_POST   = 0x400000, // +QHTTPPOST: 0,400,150
    AT_LTE_RESP_HTTP_READ   = 0x800000, // +QHTTPREAD: 0
    AT_LTE_RESP_ERROR       = 0x80000000, // +CME ERROR
#if ES_GPS_ENABLE
    // AT_LTE_RESP_GPSGNMEA    = 0x1000000, // +QGPSGNMEA
    AT_LTE_RESP_GPSLOC      = 0x2000000, // +QGPSLOC
#endif
} at_lte_resp_type_e;

typedef enum {
    HAL_LTE_DATA_NONE,
    HAL_LTE_DATA_MQTT,
    HAL_LTE_DATA_HTTP_POST,
    HAL_LTE_DATA_HTTP_GET
} hal_lte_data_type_e;

typedef struct {
    ES_CHAR cmd[LTE_AT_CMD_TX_LEN];
    ES_BYTE *data_buf;  // ref LTE_AT_DATA_TX_LEN
    ES_VOID *param;
    ES_U32 data_buf_len;    // 1024 or 16*1024(for jpg)
    ES_U16 data_len;
    ES_U8 data_type;
    ES_U8 fail_count;
} hal_lte_at_cmd_t;


static hal_lte_at_cmd_t lte_at_list[LTE_AT_LIST_COUNT] = {
    {.data_buf_len = 512},
    {.data_buf_len = 512},
    {.data_buf_len = 1024},
    {.data_buf_len = 16*1024}
};
static es_http_header_t *http_headers = ES_NULL;
static ES_U8 lte_at_idx = 0;
static ES_U32 http_conent_length = 0;
static ES_U32 file_open_handle = 0;
static ES_U32 file_read_size = 0;

static ES_VOID *rx_cirle_buf = ES_NULL;
static hal_lte_status_e lte_status = HAL_LTE_STATUS_POWER_ON;
static ES_U32 lte_now_at_resp = AT_LTE_RESP_NONE;
static ES_U32 lte_wait_at_resp = AT_LTE_RESP_NONE;
static ES_U32 lte_at_send_time = 0;
static ES_U32 lte_at_send_timeout_ms = 0;
static ES_BOOL lte_is_doing_at = ES_FALSE;
static es_hal_lte_cell_t lte_cell_info;
static ES_CHAR lte_ipaddr_str[ES_IPADDR_STR_LEN] = {0};

static ES_CHAR hal_lte_imei[ES_LTE_IMEI_LEN+1] = {0};
static ES_CHAR hal_lte_iccid[ES_LTE_ICCID_LEN+1] = {0};
static ES_U8 hal_lte_rssi = 0;
static ES_BYTE hal_lte_resp_readline[LTE_UART_RX_BUF_SIZE] = {0};
static ES_U16 hal_lte_resp_readline_len = 0;
static ES_U8 hal_lte_at_no_resp_count = 0;


static es_hal_lte_mqtt_sub_cb mqtt_sub_cb = ES_NULL;
static es_hal_lte_data_cb http_data_cb = ES_NULL;

static struct pt pt_lte_at_send;
static struct pt_sem sem_lte_at_resp;
#if ES_GPS_ENABLE
static ES_BOOL es_gps_inited = ES_FALSE;
static es_gps_info_t gps_info;
static ES_U32 gps_update_time = 0;
#endif
static ES_U8 hal_lte_get_idle_at_buf(ES_U16 data_len)
{
    ES_U8 i;

    for (i = 0; i < LTE_AT_LIST_COUNT; i++) {
        if (0 == lte_at_list[i].cmd[0] && lte_at_list[i].data_buf_len >= data_len) {
            return i;
        }
    }

    return LTE_AT_LIST_COUNT;
}


static ES_VOID hal_lte_reset_at_buf(ES_U8 idx)
{
    // es_memset(&lte_at_list[idx], 0x00, sizeof(hal_lte_at_cmd_t));
    es_memset(lte_at_list[idx].cmd, 0x00, LTE_AT_CMD_TX_LEN);
    lte_at_list[idx].data_len = 0;
    lte_at_list[idx].data_type = HAL_LTE_DATA_NONE;
    lte_at_list[idx].fail_count = 0;
    lte_at_list[idx].param = ES_NULL;
}

static ES_VOID hal_lte_update_at_idx(ES_VOID)
{
    ES_U8 i = 0;

    if (0 != lte_at_list[lte_at_idx].cmd[0]) {
        return;
    }

    for (i = 0; i < LTE_AT_LIST_COUNT; i++) {
        if (0 != lte_at_list[i].cmd[0]) {
            lte_at_idx = i;
            return;
        }
    }
}

static ES_VOID hal_lte_set_at_cmd_fail(ES_VOID)
{
    if (0 == lte_at_list[lte_at_idx].cmd[0]) {
        return;
    }

    lte_at_list[lte_at_idx].fail_count++;
    if (lte_at_list[lte_at_idx].fail_count < 3) {
        return;
    }
    
    hal_lte_debug("at cmd fail");
    hal_lte_reset_at_buf(lte_at_idx);
    lte_at_idx = (lte_at_idx + 1) % LTE_AT_LIST_COUNT;
}

static ES_S32 hal_lte_uart_send(const ES_BYTE *data, ES_U32 len)
{
#define ES_LTE_UART_SEND_PACKAGE_SIZE       (1024)
#define ES_LTE_UART_SEND_WAIT_MS            (10)

#if ES_LTE_UART_SEND_PACKAGE_SIZE
    // hal_lte_debug("lte send(%d):%s", len, data);
    ES_U32 finish_size = 0;
    ES_U32 send_size = 0;
    ES_U32 send_time = 0;

    // for debug at command
#if 0
    if (('A' == data[0] && 'T' == data[1]) || ('{' == data[0])) {
        hal_lte_debug("lte send(%d):%s", len, data);
    }
#endif

    if (len < 1024) {
        return es_uart_write(ES_4GLTE_UART_ID, data, len);
    }

    send_time = es_time_get_sytem_ms();
    do {
        send_size = len - finish_size;
        if (send_size > ES_LTE_UART_SEND_PACKAGE_SIZE) {
            send_size = ES_LTE_UART_SEND_PACKAGE_SIZE;
        }

        if (send_size != es_uart_write(ES_4GLTE_UART_ID, (const ES_BYTE *)(data+finish_size), send_size)) {
            return finish_size;
        }
        finish_size += send_size;
        es_os_msleep(ES_LTE_UART_SEND_WAIT_MS);
    } while (finish_size < len);

    printk("uart send %d bytes, time:%d\r\n", finish_size, es_time_get_sytem_ms()-send_time);
    return finish_size;
#else
    return es_uart_write(ES_4GLTE_UART_ID, data, len);
#endif
}


static ES_S32 hal_lte_exec_cmd(const char *cmd_format, ...)
{
    ES_CHAR send_buf[LTE_AT_CMD_TX_LEN] = {0};
    ES_U32 send_len;
    es_va_list args;

    es_va_start(args, cmd_format);
    send_len = es_vsnprintf((char *)send_buf, LTE_AT_CMD_TX_LEN, cmd_format, args);
    es_va_end(args);

    return hal_lte_uart_send((const ES_BYTE *)send_buf, send_len);
}

static ES_VOID hal_lte_reset_send_param(ES_U32 wait_resp, ES_U32 timeout_ms)
{
    lte_wait_at_resp = wait_resp;
    lte_now_at_resp = AT_LTE_RESP_NONE;
    lte_at_send_timeout_ms = timeout_ms;
    lte_at_send_time = es_time_get_sytem_ms();
}

static ES_VOID hal_lte_at_send_sem_check(ES_VOID)
{
    ES_BOOL got_resp_or_timeout = ES_FALSE;

    if (0 == lte_at_send_time) {
        return;
    }

    if (lte_now_at_resp & lte_wait_at_resp) {
        got_resp_or_timeout = ES_TRUE;
        hal_lte_at_no_resp_count = 0;
    }

    if (ES_RET_SUCCESS == es_time_check_timeout_ms(&lte_at_send_time, lte_at_send_timeout_ms)) {
        got_resp_or_timeout = ES_TRUE;
        hal_lte_at_no_resp_count++;
        hal_lte_debug("wait resp timeout:%d, no resp_count:%d", lte_at_send_timeout_ms, hal_lte_at_no_resp_count);
    }

    if (got_resp_or_timeout) {
        lte_at_send_time = 0;
        PT_SEM_SIGNAL(pt, &sem_lte_at_resp);
    }

    if (hal_lte_at_no_resp_count > 5) {
        hal_lte_at_no_resp_count = 0;
        lte_status = HAL_LTE_STATUS_TRY_RESET;
    }
}

static PT_THREAD(hal_lte_reset_init(struct pt *pt))
{
    static ES_U32 last_time = 1000;
    static ES_U32 last_reset_time = 0;
    static ES_U32 next_reset_time = 30*1000;

    PT_BEGIN(pt);
    lte_status = HAL_LTE_STATUS_TRY_RESET;
    if (ES_RET_SUCCESS != es_time_check_timeout_ms(&last_reset_time, next_reset_time)) {
        goto TIMEOUT_FAIL;
    }
    lte_is_doing_at = 1;
    // // 1 try reset by AT command
    hal_lte_debug(" try reset by AT command");
    LTE_SEND_CMD_WAIT_RESP_WITHOUT_GOEND("ATE0\r\n", AT_LTE_RESP_OK, 1000);
    if (0 == (AT_LTE_RESP_OK & lte_now_at_resp)) {
        hal_lte_debug("no resp or recv err");
        goto RESET_POWER;
    }

    hal_lte_debug("send reset at command");
    LTE_SEND_CMD_WAIT_RESP_WITHOUT_GOEND("AT+CFUN=1,1\r\n", AT_LTE_RESP_OK, 10000);
    if (0 == (AT_LTE_RESP_OK & lte_now_at_resp)) {
        hal_lte_debug("no resp or recv err");
        goto RESET_POWER;
    }

    // wait system boot
    last_time = es_time_get_sytem_ms();
    PT_WAIT_UNTIL(pt, (ES_RET_SUCCESS == es_time_check_timeout_ms(&last_time, 5*1000)));
    goto RESET_END;

    // 2 try reset by reset power
RESET_POWER:
    hal_lte_debug("try reset by reset power");
    gpiohs_set_pin(ES_4GLTE_RESET_HS_NUM, ES_GPIO_VAL_HIGH);
    last_time = es_time_get_sytem_ms();
    PT_WAIT_UNTIL(pt, (ES_RET_SUCCESS == es_time_check_timeout_ms(&last_time, 1000)));
    gpiohs_set_pin(ES_4GLTE_RESET_HS_NUM, ES_GPIO_VAL_LOW);
    last_time = es_time_get_sytem_ms();
    PT_WAIT_UNTIL(pt, (ES_RET_SUCCESS == es_time_check_timeout_ms(&last_time, 1000)));

RESET_END:
    lte_status = HAL_LTE_STATUS_POWER_ON;
    if (next_reset_time > 3600*1000) {
        next_reset_time = 30*1000;
    } else {
        next_reset_time = next_reset_time + 30*1000;
    }

TIMEOUT_FAIL:
    lte_is_doing_at = 0;
    PT_END(pt);
}

static PT_THREAD(hal_lte_power_on_init(struct pt *pt))
{
#if 0//ES_4GLTE_POWER_PIN
    static ES_U32 last_time = 1000;
    PT_BEGIN(pt);

    lte_status = HAL_LTE_STATUS_POWER_ON;
    if (ES_RET_SUCCESS != es_time_check_timeout_ms(&last_time, 10*1000)) {
        goto END;
    }
    lte_is_doing_at = 1;

    hal_lte_debug("lte power off start...");
    gpiohs_set_pin(ES_4GLTE_POWER_HS_NUM, ES_GPIO_VAL_HIGH);
    msleep(670);
    gpiohs_set_pin(ES_4GLTE_POWER_HS_NUM, ES_GPIO_VAL_LOW);
    hal_lte_debug("lte power off end...");
    last_time = es_time_get_sytem_ms();
    PT_WAIT_UNTIL(pt, (ES_RET_SUCCESS == es_time_check_timeout_ms(&last_time, 5*1000)));

    hal_lte_debug("lte power on start...");
    gpiohs_set_pin(ES_4GLTE_POWER_HS_NUM, ES_GPIO_VAL_HIGH);
    msleep(750);
    gpiohs_set_pin(ES_4GLTE_POWER_HS_NUM, ES_GPIO_VAL_LOW);
    hal_lte_debug("lte power on end...");

    last_time = es_time_get_sytem_ms();
    PT_WAIT_UNTIL(pt, (ES_RET_SUCCESS == es_time_check_timeout_ms(&last_time, 8*1000)));
    lte_status = HAL_LTE_STATUS_SYS_INIT;
END:
    lte_is_doing_at = 0;
    PT_END(pt);
#else
    lte_status = HAL_LTE_STATUS_SYS_INIT;
    return 0;
#endif
}

static PT_THREAD(hal_lte_sys_at_init(struct pt *pt))
{
    static ES_U32 last_time = 0;
    static ES_U8 sys_init_fail_count = 0;
    ES_BOOL end_with_timeout = ES_FALSE;

    PT_BEGIN(pt);
    lte_status = HAL_LTE_STATUS_SYS_INIT;

    if (ES_RET_SUCCESS != es_time_check_timeout_ms(&last_time, 5*1000)) {
        end_with_timeout = ES_TRUE;
        goto END;
    }
    lte_is_doing_at = 1;

    // LTE_SEND_CMD_WAIT_RESP("ATI\r\n", AT_LTE_RESP_OK, 1000);
    LTE_SEND_CMD_WAIT_RESP("ATE0\r\n", AT_LTE_RESP_OK, 1000);
    LTE_SEND_CMD_WAIT_RESP("AT+CPIN?\r\n", AT_LTE_RESP_CPIN, 1000);

    if (0 == hal_lte_imei[0]) {
        // AT+GSN
        LTE_SEND_CMD_WAIT_RESP("AT+GSN\r\n", AT_LTE_RESP_GSN, 1000);
    }

     // AT+QCCID
    if (0 == hal_lte_iccid[0]) {
        LTE_SEND_CMD_WAIT_RESP("AT+QCCID\r\n", AT_LTE_RESP_ICCID, 1000);
    }

    LTE_SEND_CMD_WAIT_RESP("AT+CSQ\r\n\r\n", AT_LTE_RESP_CSQ, 1000);

    LTE_SEND_CMD_WAIT_RESP("AT+QICSGP=1,1,\"CTNET\",\"\",\"\",1\r\n", AT_LTE_RESP_OK, 1000);

    LTE_SEND_CMD_WAIT_RESP("AT+QIACT?\r\n\r\n", AT_LTE_RESP_OK, 1000);

    // AT+CEREG?    
    LTE_SEND_CMD_WAIT_RESP("AT+CGREG?\r\n", AT_LTE_RESP_CGREG, 1000);

    // AT+CEREG=2
    LTE_SEND_CMD_WAIT_RESP("AT+CREG=2\r\n", AT_LTE_RESP_OK, 1000);

    // AT+CREG
    LTE_SEND_CMD_WAIT_RESP("AT+CREG?\r\n", AT_LTE_RESP_CELLID, 1000);


    lte_status = HAL_LTE_STATUS_NET_REG;

END:
    if (ES_FALSE == end_with_timeout) {
        if (HAL_LTE_STATUS_NET_REG != lte_status) {
            sys_init_fail_count++;
        }
        hal_lte_debug("sys_init_fail_count:%d", sys_init_fail_count);
        if (sys_init_fail_count > 5) {
            sys_init_fail_count = 0;
            lte_status = HAL_LTE_STATUS_TRY_RESET;
        }
    }

    lte_is_doing_at = 0;
    PT_END(pt);
}


static PT_THREAD(hal_lte_net_reg_init(struct pt *pt))
{
    static ES_U32 last_time = 0;
    static ES_U8 net_reg_fail_count = 0;
    ES_BOOL end_with_timeout = ES_FALSE;

    PT_BEGIN(pt);
    lte_status = HAL_LTE_STATUS_NET_REG;

    if (ES_RET_SUCCESS != es_time_check_timeout_ms(&last_time, 5*1000)) {
        end_with_timeout = ES_TRUE;
        goto END;
    }
    lte_is_doing_at = 1;

    // LTE_SEND_CMD_WAIT_RESP("AT+QICSGP=1,1,\"cmnet\",\"\",\"\"\r\n", AT_LTE_RESP_OK, 1000);
    LTE_SEND_CMD_WAIT_RESP("AT+QISDE=0\r\n", AT_LTE_RESP_OK, 10000);
    LTE_SEND_CMD_WAIT_RESP("AT+QIDEACT=1\r\n", AT_LTE_RESP_OK, 10000);
    LTE_SEND_CMD_WAIT_RESP("AT+QIACT=1\r\n", AT_LTE_RESP_OK, 10000);
    LTE_SEND_CMD_WAIT_RESP("AT+CGPADDR\r\n", AT_LTE_RESP_CGPADDR, 10000);

#if ES_GPS_ENABLE
    // open gsp
    if (!es_gps_inited) {
        LTE_SEND_CMD_WAIT_RESP("AT+QGPS=1\r\n", AT_LTE_RESP_OK, 1000);
        es_gps_inited = ES_TRUE;
    }
#endif

    lte_status = HAL_LTE_STATUS_MQTT_CONN;
    net_reg_fail_count = 0;

END:
    if (ES_FALSE == end_with_timeout) {
        if (HAL_LTE_STATUS_MQTT_CONN != lte_status) {
            net_reg_fail_count++;
        }

        if (net_reg_fail_count > 3) {
            net_reg_fail_count = 0;
            lte_status = HAL_LTE_STATUS_TRY_RESET;
        }
    }
    
    lte_is_doing_at = 0;
    PT_END(pt);
}



static PT_THREAD(hal_lte_mqtt_serv_conn(struct pt *pt))
{
    static ES_U32 last_time = 0;
    static ES_U8 mqtt_fail_count = 0;
    ES_CHAR lte_at_cmd[LTE_AT_CMD_TX_LEN] = {0};
    ES_CHAR lte_mqtt_id[ES_MAC_COLON_STR_LEN+1] = {0};
    ES_BOOL end_with_timeout = ES_FALSE;

    PT_BEGIN(pt);
    lte_status = HAL_LTE_STATUS_MQTT_CONN;

    if (ES_RET_SUCCESS != es_time_check_timeout_ms(&last_time, 5*1000)) {
        end_with_timeout = ES_TRUE;
        goto END;
    }
    lte_is_doing_at = 1;

    hal_lte_debug("try conn mqtt serv");

    // 1 disconnect and close mqtt.
    // LTE_SEND_CMD_WAIT_RESP("AT+QMTDISC=0\r\n", AT_LTE_RESP_OK, 1000);
    LTE_SEND_CMD_WAIT_RESP("AT+QMTCFG=\"recv/mode\",0,0,1\r\n", AT_LTE_RESP_OK, 1000);

    // 2 open mqtt
    es_snprintf(lte_at_cmd, LTE_AT_CMD_TX_LEN, "AT+QMTOPEN=0,\"%s\",%d\r\n",
                ES_MQTT_CFG_IP, ES_MQTT_CFG_PORT);
    LTE_SEND_CMD_WAIT_RESP(lte_at_cmd, AT_LTE_RESP_MQTOPEN, 15000);

    // 3 connect mqtt
    if (ES_RET_SUCCESS != es_hal_lte_get_mqtt_id(lte_mqtt_id)) {
        lte_status = HAL_LTE_STATUS_SYS_INIT;
        hal_lte_debug("get mqtt id fail");
        goto END;
    }
    es_snprintf(lte_at_cmd, LTE_AT_CMD_TX_LEN, "AT+QMTCONN=0,\"%s\",\"%s\",\"%s\"\r\n",
                lte_mqtt_id, ES_MQTT_CFG_USERNAME, ES_MQTT_CFG_PASSWD);
    LTE_SEND_CMD_WAIT_RESP(lte_at_cmd, AT_LTE_RESP_MQTCONN, 15000);

    // 5 mqtt subscribe
    // ES_MQTT_CFG_DEV_SUB_TOPIC_FMT
    es_hal_lte_get_mqtt_id(lte_mqtt_id);
    es_snprintf(lte_at_cmd, LTE_AT_CMD_TX_LEN, "AT+QMTSUB=0,1,\"forklift/%s\",0\r\n",
                lte_mqtt_id);
    LTE_SEND_CMD_WAIT_RESP(lte_at_cmd, AT_LTE_RESP_MQTSUB, 3000);

    lte_status = HAL_LTE_STATUS_NET_READY;
    mqtt_fail_count = 0;

END:
    if (ES_FALSE == end_with_timeout) {
        if (HAL_LTE_STATUS_NET_READY != lte_status) {
            // close mqtt
            LTE_SEND_CMD_WAIT_RESP_WITHOUT_GOEND("AT+QMTDISC=0\r\n", AT_LTE_RESP_OK, 10000);
            mqtt_fail_count++;
            hal_lte_debug("mqtt_fail_count:%d", mqtt_fail_count);
        }

        if (mqtt_fail_count > 0 && mqtt_fail_count < 3) {
            lte_status = HAL_LTE_STATUS_NET_REG;
        } else if (mqtt_fail_count >= 3) {
            lte_status = HAL_LTE_STATUS_TRY_RESET;
            mqtt_fail_count = 0;
        }
    }
    
    lte_is_doing_at = 0;
    PT_END(pt);
}

static PT_THREAD(hal_lte_send_mqtt_data(struct pt *pt))
{
    PT_BEGIN(pt);

    lte_is_doing_at = 1;
    LTE_SEND_CMD_WAIT_RESP(lte_at_list[lte_at_idx].cmd, AT_LTE_RESP_SEND_FLAG, 1000);
    hal_lte_uart_send(lte_at_list[lte_at_idx].data_buf, lte_at_list[lte_at_idx].data_len);

    hal_lte_reset_at_buf(lte_at_idx);
    lte_at_idx = (lte_at_idx + 1) % LTE_AT_LIST_COUNT;

END:
    lte_is_doing_at = 0;
    PT_END(pt);
}


static PT_THREAD(hal_lte_send_http_post(struct pt *pt))
{
    ES_CHAR cmd[LTE_AT_CMD_TX_LEN] = {0};
    es_http_header_t *http_header = ES_NULL;
    static ES_U32 i = 0;

    PT_BEGIN(pt);
    lte_is_doing_at = 1;
    // 1 set http config
    LTE_SEND_CMD_WAIT_RESP("AT+QHTTPCFG=\"contextid\",1\r\n", AT_LTE_RESP_OK, 1000);
    // LTE_SEND_CMD_WAIT_RESP("AT+QHTTPCFG=\"requestheader\",1\r\n", AT_LTE_RESP_OK, 1000);

    // 2 set url
    es_snprintf(cmd, LTE_AT_CMD_TX_LEN, "AT+QHTTPURL=%d,80\r\n", (ES_U32)es_strlen(lte_at_list[lte_at_idx].cmd));
    LTE_SEND_CMD_WAIT_RESP(cmd, AT_LTE_RESP_HTTP_SEND, 10000);
    LTE_SEND_DATA_WAIT_RESP((const ES_BYTE *)(lte_at_list[lte_at_idx].cmd), 
            (ES_U32)es_strlen(lte_at_list[lte_at_idx].cmd), AT_LTE_RESP_OK, 1000);

    // 3 set header
    // es_snprintf(cmd, LTE_AT_CMD_TX_LEN, "AT+QHTTPCFG=\"reqheader/add\",Content-Length,%d\r\n", lte_at_list[lte_at_idx].data_len);
    // LTE_SEND_CMD_WAIT_RESP(cmd, AT_LTE_RESP_OK, 1000);
    if (ES_NULL != lte_at_list[lte_at_idx].param) {
        i = 0;
        do {
            http_header = (es_http_header_t *)lte_at_list[lte_at_idx].param;
            if (0 == http_header[i].name[0] || 0 == http_header[i].value[0]) {
                break;
            }
            hal_lte_debug("http header, i=%d", i);
            es_snprintf(cmd, LTE_AT_CMD_TX_LEN, "AT+QHTTPCFG=\"reqheader/add\",%s,\"%s\"\r\n", http_header[i].name, http_header[i].value);
            LTE_SEND_CMD_WAIT_RESP(cmd, AT_LTE_RESP_OK, 1000);
            i++;
        } while (i < ES_HTTP_HEADER_MAX_COUNT);
    }
    // LTE_SEND_CMD_WAIT_RESP("AT+QHTTPCFG?\r\n", AT_LTE_RESP_OK, 1000);

    // 4 post data
    es_snprintf(cmd, LTE_AT_CMD_TX_LEN, "AT+QHTTPPOST=%d,80,80\r\n", (ES_U32)lte_at_list[lte_at_idx].data_len);
    LTE_SEND_CMD_WAIT_RESP(cmd, AT_LTE_RESP_HTTP_SEND, 10000);
    LTE_SEND_DATA_WAIT_RESP((const ES_BYTE *)(lte_at_list[lte_at_idx].data_buf), 
            (ES_U32)lte_at_list[lte_at_idx].data_len, AT_LTE_RESP_HTTP_POST, 10*1000);

    // 5 read response
    LTE_SEND_CMD_WAIT_RESP("AT+QHTTPREAD=80\r\n", AT_LTE_RESP_HTTP_READ, 1000);

    hal_lte_reset_at_buf(lte_at_idx);
    lte_at_idx = (lte_at_idx + 1) % LTE_AT_LIST_COUNT;

END:
    LTE_SEND_CMD_WAIT_RESP_WITHOUT_GOEND("AT+QHTTPSTOP\r\n", AT_LTE_RESP_OK, 1000);
    lte_is_doing_at = 0;
    hal_lte_set_at_cmd_fail();
    PT_END(pt);
}

static PT_THREAD(hal_lte_send_http_get(struct pt *pt))
{
    ES_CHAR cmd[LTE_AT_CMD_TX_LEN] = {0};
    static ES_U32 read_size = 0;
    static ES_U32 finish_size = 0;
    static ES_BOOL success = 0;

    PT_BEGIN(pt);
    success = 1;
    lte_is_doing_at = 1;
    // 1 set http config
    LTE_SEND_CMD_WAIT_RESP("AT+QHTTPCFG=\"contextid\",1\r\n", AT_LTE_RESP_OK, 1000);
    // LTE_SEND_CMD_WAIT_RESP("AT+QHTTPCFG=\"responseheader\",1\r\n", AT_LTE_RESP_OK, 1000);
    LTE_SEND_CMD_WAIT_RESP("AT+QFDEL=\"*\"\r\n", AT_LTE_RESP_OK, 1000);
    LTE_SEND_CMD_WAIT_RESP("AT+QFLDS=\"UFS\"\r\n", AT_LTE_RESP_OK, 1000);

    // 2 set url
    es_snprintf(cmd, LTE_AT_CMD_TX_LEN, "AT+QHTTPURL=%d,80\r\n", (ES_U32)es_strlen(lte_at_list[lte_at_idx].cmd));
    LTE_SEND_CMD_WAIT_RESP(cmd, AT_LTE_RESP_HTTP_SEND, 10000);
    LTE_SEND_DATA_WAIT_RESP((const ES_BYTE *)(lte_at_list[lte_at_idx].cmd), 
            (ES_U32)es_strlen(lte_at_list[lte_at_idx].cmd), AT_LTE_RESP_OK, 1000);

    // 3 send http get
    http_conent_length = 0;
    LTE_SEND_CMD_WAIT_RESP("AT+QHTTPGET=50\r\n", AT_LTE_RESP_HTTP_GET, 60*1000);
    if (0 == http_conent_length) {
        goto END;
    }

    // 4 save to filesystem
    LTE_SEND_CMD_WAIT_RESP("AT+QHTTPREADFILE=\"UFS:httpget.bin\",50\r\n", AT_LTE_RESP_HTTP_FREAD, 60*1000);

    // 5 read data from  filesystem
    LTE_SEND_CMD_WAIT_RESP("AT+QFOPEN=\"httpget.bin\",0\r\n", AT_LTE_RESP_FILE_OPEN, 1000);
    es_snprintf(cmd, LTE_AT_CMD_TX_LEN, "AT+QFSEEK=%d,0,0\r\n",file_open_handle);
    LTE_SEND_CMD_WAIT_RESP(cmd, AT_LTE_RESP_OK, 1000);
    if (http_data_cb) {
        http_data_cb(HTTP_EVENT_CONTENT_LEN, ES_NULL, http_conent_length);
    }
    do {
        read_size = (http_conent_length - finish_size);
        if (read_size > 1024) {
            read_size = 1024;
        }
        file_read_size = 0;
        es_snprintf(cmd, LTE_AT_CMD_TX_LEN, "AT+QFREAD=%d,%d\r\n", file_open_handle, read_size);
        LTE_SEND_CMD_WAIT_RESP(cmd, AT_LTE_RESP_FILE_READ, 10000);
        hal_lte_debug("file_read_size:%d", file_read_size);
        PT_WAIT_UNTIL(pt, (0 == file_read_size));
        finish_size += read_size;
    } while (finish_size != http_conent_length);

    hal_lte_reset_at_buf(lte_at_idx);
    lte_at_idx = (lte_at_idx + 1) % LTE_AT_LIST_COUNT;
    success = 1;

END:
    if (http_data_cb) {
        http_data_cb(HTTP_EVENT_DOWNLOAD_END, ES_NULL, success);
    }
    es_snprintf(cmd, LTE_AT_CMD_TX_LEN, "AT+QFCLOSE=%d\r\n", file_open_handle);
    LTE_SEND_CMD_WAIT_RESP_WITHOUT_GOEND(cmd, AT_LTE_RESP_OK, 1000);
    LTE_SEND_CMD_WAIT_RESP_WITHOUT_GOEND("AT+QHTTPSTOP\r\n", AT_LTE_RESP_OK, 1000);
    LTE_SEND_CMD_WAIT_RESP_WITHOUT_GOEND("AT+QFDEL=\"*\"\r\n", AT_LTE_RESP_OK, 1000);
    lte_is_doing_at = 0;
    success = 0;
    hal_lte_set_at_cmd_fail();
    PT_END(pt);
}

#if ES_GPS_ENABLE
static PT_THREAD(hal_lte_gps_update(struct pt *pt))
{
    ES_U32 now_time;
    PT_BEGIN(pt);

    now_time = es_time_get_timestamp();
    if ((now_time - gps_update_time) < LTE_GPS_UPDATE_TIME_SEC) {
        goto END;
    }
    lte_is_doing_at = 1;
    gps_update_time = now_time;

    // LTE_SEND_CMD_WAIT_RESP("AT+QGPSGNMEA=\"GGA\"\r\n", AT_LTE_RESP_OK, 1000);
    // LTE_SEND_CMD_WAIT_RESP("AT+QGPSGNMEA=\"RMC\"\r\n", AT_LTE_RESP_OK, 1000);
    // LTE_SEND_CMD_WAIT_RESP("AT+QGPSGNMEA=\"VTG\"\r\n", AT_LTE_RESP_OK, 1000);

    LTE_SEND_CMD_WAIT_RESP("AT+QGPSLOC=2\r\n", AT_LTE_RESP_OK, 1000);
    // hal_lte_exec_cmd("AT+QGPSLOC=2\r\n");

END:
    lte_status = HAL_LTE_STATUS_NET_READY;
    lte_is_doing_at = 0;
    PT_END(pt);
}
#endif

static ES_VOID hal_lte_send_data(struct pt *pt)
{
    hal_lte_update_at_idx();
    if (0 == lte_at_list[lte_at_idx].cmd[0]) {
#if ES_GPS_ENABLE
        lte_status = HAL_LTE_STATUS_UPDATE_GPS;
#endif
        return;
    }

    if (HAL_LTE_DATA_MQTT == lte_at_list[lte_at_idx].data_type) {
        hal_lte_send_mqtt_data(pt);
    } else if (HAL_LTE_DATA_HTTP_POST == lte_at_list[lte_at_idx].data_type) {
        hal_lte_send_http_post(pt);
    } else if (HAL_LTE_DATA_HTTP_GET == lte_at_list[lte_at_idx].data_type) {
        hal_lte_send_http_get(pt);
    } else {
        hal_lte_reset_at_buf(lte_at_idx);
        lte_at_idx = (lte_at_idx + 1) % LTE_AT_LIST_COUNT;
    }
}


static ES_VOID hal_lte_uart_rx_cb(ES_U8 id, const ES_BYTE *buf, ES_U16 len)
{
    es_circel_write(rx_cirle_buf, buf, len);
}

static ES_BOOL hal_lte_try_readline(ES_VOID)
{
    ES_U32 buf_size;
    ES_BYTE c;
    ES_BYTE last_c = 0;

    buf_size = es_circel_get_data_size(rx_cirle_buf);
    if (0 == buf_size) {
        return ES_FALSE;
    }

    // http data
    if (0 != file_read_size && ES_NULL != http_data_cb) {
        if (buf_size < file_read_size) {
            return ES_FALSE;
        }
        hal_lte_debug("file_read_size:%d", file_read_size);
        es_circel_read(rx_cirle_buf, (const ES_BYTE *)hal_lte_resp_readline, file_read_size);
        hal_lte_debug("file_read_size:%d", file_read_size);
        if (http_data_cb) {
            http_data_cb(HTTP_EVENT_BODY_DATA, hal_lte_resp_readline, file_read_size);
        }
        file_read_size = 0;
        return ES_FALSE;
    }

    if (hal_lte_resp_readline_len > 0) {
        last_c = hal_lte_resp_readline[hal_lte_resp_readline_len-1];
    }
    // try read line
    while (1) {
        if (ES_RET_SUCCESS != es_circel_read_byte(rx_cirle_buf, &c)) {
            break;
        }

        hal_lte_resp_readline[hal_lte_resp_readline_len] = c;
        hal_lte_resp_readline_len++;
        if ('\r' == last_c && '\n' == c) {
            if (2 == hal_lte_resp_readline_len) {
                hal_lte_resp_readline_len = 0;
                last_c = 0;
                //hal_lte_debug("reset hal_lte_resp_readline_len");
                continue;
            }  else {
                hal_lte_resp_readline[hal_lte_resp_readline_len] = 0;
                return ES_TRUE;
            }
        }

        if (1 == hal_lte_resp_readline_len && '>' == c) {
            hal_lte_resp_readline[hal_lte_resp_readline_len++] = '\r';
            hal_lte_resp_readline[hal_lte_resp_readline_len++] = '\n';
            hal_lte_resp_readline[hal_lte_resp_readline_len] = 0;
            return ES_TRUE;
        }
        last_c = c;

        // reset, buffer overflow
        if (LTE_UART_RX_BUF_SIZE == hal_lte_resp_readline_len) {
            hal_lte_resp_readline[hal_lte_resp_readline_len-1] = 0;
            hal_lte_error("%s", hal_lte_resp_readline);
            hal_lte_error("reset readline buffer.");
            hal_lte_resp_readline_len = 0;
        }
    }

    hal_lte_resp_readline[hal_lte_resp_readline_len] = 0;
    return ES_FALSE;
}

/*
+QMTRECV: 0,0,"topic/pub",30,This is test data, hello MQTT
*/
static ES_VOID hal_lte_mqtt_sub_parser(ES_CHAR *data)
{
    ES_CHAR *p = ES_NULL;
    const ES_CHAR *topic = ES_NULL;
    const ES_BYTE *payload = ES_NULL;
    ES_U16 data_len = 0;

    // skip "+QMTRECV: 0,0,\""
    p = (ES_CHAR *)(data+15);
    topic = ( const ES_CHAR *)p;
    p++;
    while ('"' != *p && 0 != *p) {
        p++;
    }
    *p = 0;
    p++;
    hal_lte_debug("topic:%s", topic);

    // skip ","
    p++;
    data_len = es_atoi(p);
    hal_lte_debug("data_len:%d", data_len);

    // skip "97 bytes,\""
    while ('"' != *p && 0 != *p) {
        p++;
    }
    p++;
    payload = (const ES_BYTE *)p;
    p[data_len] = 0;

    if (mqtt_sub_cb) {
        mqtt_sub_cb(topic, payload, data_len);
    }

}


static ES_VOID hal_lte_at_parser(ES_VOID)
{
    ES_S32 tmp_int = 0;
    const ES_CHAR *p = ES_NULL;
    ES_U8 i = 0;
    ES_U16 resp_readline_len;

    if (ES_NULL == rx_cirle_buf) {
        return;
    }

    do {
        // 1 read data for buffer
        if (!hal_lte_try_readline()) {
            return;
        }
        resp_readline_len = hal_lte_resp_readline_len;
        hal_lte_resp_readline_len = 0;

        hal_lte_debug("lte recv:%s", hal_lte_resp_readline);
        if (es_string_start_with((const ES_CHAR *)hal_lte_resp_readline, "OK")) {
            lte_now_at_resp |= AT_LTE_RESP_OK;
        } else if (es_string_start_with((const ES_CHAR *)hal_lte_resp_readline, "+QMTRECV: 0,0,\"")) {
            // recv mqtt data
            hal_lte_mqtt_sub_parser((ES_CHAR *)hal_lte_resp_readline);
        } else if (es_string_start_with((const ES_CHAR *)hal_lte_resp_readline, ">")) {
            lte_now_at_resp |= AT_LTE_RESP_SEND_FLAG;
        } else if (es_string_start_with((const ES_CHAR *)hal_lte_resp_readline, "+CPIN: READY")) {
            lte_now_at_resp |= AT_LTE_RESP_CPIN;
        } else if (es_string_start_with((const ES_CHAR *)hal_lte_resp_readline, "86")) {
            lte_now_at_resp |= AT_LTE_RESP_GSN;
            es_strncpy(hal_lte_imei, (const ES_CHAR *)(hal_lte_resp_readline), ES_LTE_IMEI_LEN);
            hal_lte_debug("lte gsn:%s", hal_lte_imei);
        } else if (es_string_start_with((const ES_CHAR *)hal_lte_resp_readline, "+QCCID: ")) {
            lte_now_at_resp |= AT_LTE_RESP_ICCID;
            es_strncpy(hal_lte_iccid, (const ES_CHAR *)(hal_lte_resp_readline+8), ES_LTE_ICCID_LEN);
            hal_lte_debug("lte iccid:%s", hal_lte_iccid);
        } else if (es_string_start_with((const ES_CHAR *)hal_lte_resp_readline, "+CSQ: ")) {
            hal_lte_rssi = (ES_U8)es_atoi((const ES_CHAR *)(hal_lte_resp_readline+6));
            if (hal_lte_rssi > 0 && hal_lte_rssi < 32) {
                lte_now_at_resp |= AT_LTE_RESP_CSQ;
            }
            hal_lte_debug("lte rssi:%d", hal_lte_rssi);
        } else if (es_string_start_with((const ES_CHAR *)hal_lte_resp_readline, "+CGREG: ")) {
            // +CEREG: 5,1,"ADE1","0F755652",9,,,"00000001","00111000"
            tmp_int = es_atoi((const ES_CHAR *)(hal_lte_resp_readline+10));
            if (1 == tmp_int || 5 == tmp_int) {
                lte_now_at_resp |= AT_LTE_RESP_CGREG;
            }
            hal_lte_debug("lte CEREG:%d", tmp_int);
        } else if (es_string_start_with((const ES_CHAR *)hal_lte_resp_readline, "+CREG: ")) {
            //+CREG: 3,1,"ad0f","0b7ea31a",7
            memset(lte_cell_info.lac, 0x00, ES_LTE_CELL_LAC_LEN);
            memset(lte_cell_info.ci, 0x00, ES_LTE_CELL_CI_LEN);
            lte_now_at_resp |= AT_LTE_RESP_CELLID;

            // skip "+CREG: 3,1,""
            p = (const ES_CHAR *)(hal_lte_resp_readline+11);
            if ('"' != p[0]) {
                es_strncpy(lte_cell_info.lac, "0000", ES_LTE_CELL_LAC_LEN);
                es_strncpy(lte_cell_info.ci, "00000000", ES_LTE_CELL_CI_LEN);
                continue;
            }
            p++;

            i = 0;
            while ('"' != p[0]) {
                lte_cell_info.lac[i] = p[0];
                p++;
                i++;
            }

            // skip ",
            p = (const ES_CHAR *)(p+2);
            if ('"' != *p) {
                es_strncpy(lte_cell_info.lac, "0000", ES_LTE_CELL_LAC_LEN);
                es_strncpy(lte_cell_info.ci, "00000000", ES_LTE_CELL_CI_LEN);
                continue;
            }
            p++; // skip "
        
            i = 0;
            while ('"' != *p) {
                lte_cell_info.ci[i] = p[0];
                p++;
                i++;
            }

            hal_lte_debug("lte cell lac:%s, ci:%s", lte_cell_info.lac, lte_cell_info.ci);
        } else if (es_string_start_with((const ES_CHAR *)hal_lte_resp_readline, "+CGPADDR: ")) {
            //+CGPADDR: 1, "***********"
            i = 0;
            p = (const ES_CHAR *)(hal_lte_resp_readline+13);
            while ('"' != *p && *p != 0) {
                lte_ipaddr_str[i] = p[0];
                p++;
                i++;
            }
            hal_lte_debug("lte ipaddr:%s", lte_ipaddr_str);
            lte_now_at_resp |= AT_LTE_RESP_CGPADDR;
        } else if (es_string_start_with((const ES_CHAR *)hal_lte_resp_readline, "+QMTOPEN: 0,0")) {
            lte_now_at_resp |= AT_LTE_RESP_MQTOPEN;
        } else if (es_string_start_with((const ES_CHAR *)hal_lte_resp_readline, "+QMTCONN: 0,0,0")) {
            lte_now_at_resp |= AT_LTE_RESP_MQTCONN;
        } else if (es_string_start_with((const ES_CHAR *)hal_lte_resp_readline, "+QMTSUB: 0,1,0,0")) {
            lte_now_at_resp |= AT_LTE_RESP_MQTSUB;
        } else if (es_string_start_with((const ES_CHAR *)hal_lte_resp_readline, "+QHTTPGET: 0,200,")) {
            lte_now_at_resp |= AT_LTE_RESP_HTTP_GET;
            http_conent_length = es_atoi((const ES_CHAR *)(hal_lte_resp_readline+17));
        } else if (es_string_start_with((const ES_CHAR *)hal_lte_resp_readline, "+QHTTPREADFILE: 0")) {
            lte_now_at_resp |= AT_LTE_RESP_HTTP_FREAD;
        } else if (es_string_start_with((const ES_CHAR *)hal_lte_resp_readline, "+QFOPEN: ")) {
            file_open_handle = (ES_U32)atoi((const ES_CHAR *)(hal_lte_resp_readline+9));
            lte_now_at_resp |= AT_LTE_RESP_FILE_OPEN;
        } else if (es_string_start_with((const ES_CHAR *)hal_lte_resp_readline, "CONNECT")) {
            lte_now_at_resp |= AT_LTE_RESP_HTTP_SEND;

            // "CONNECT 1024"
            if (es_string_start_with((const ES_CHAR *)hal_lte_resp_readline, "CONNECT ")) {
                lte_now_at_resp |= AT_LTE_RESP_FILE_READ;
                file_read_size = (ES_U32)atoi((const ES_CHAR *)(hal_lte_resp_readline+8));
            }
        } else if (es_string_start_with((const ES_CHAR *)hal_lte_resp_readline, "+QHTTPPOST: 0,")) {
            lte_now_at_resp |= AT_LTE_RESP_HTTP_POST;
        } else if (es_string_start_with((const ES_CHAR *)hal_lte_resp_readline, "+QHTTPREAD: 0")) {
            lte_now_at_resp |= AT_LTE_RESP_HTTP_READ;
        } else if (es_string_start_with((const ES_CHAR *)hal_lte_resp_readline, "+CME ERROR: ")) {
            lte_now_at_resp |= AT_LTE_RESP_ERROR;
            lte_now_at_resp |= lte_wait_at_resp;
#if ES_GPS_ENABLE
        // } else if (es_string_start_with((const ES_CHAR *)hal_lte_resp_readline, "+QGPSGNMEA: $GN")) {
        //     es_gps_minmea_parse((const ES_CHAR *)(hal_lte_resp_readline+12), &gps_info);
        //     lte_now_at_resp |= AT_LTE_RESP_GPSGNMEA;
        } else if (es_string_start_with((const ES_CHAR *)hal_lte_resp_readline, "+QGPSLOC")) {
            if (ES_RET_SUCCESS == es_gps_minmea_parse((const ES_CHAR *)(hal_lte_resp_readline+3), &gps_info)) {
                es_log_info("ES_RET_SUCCESS == es_gps_minmea_parse");
                es_spec_car_update_gps_cache_str((const ES_CHAR *)(hal_lte_resp_readline+3));
            }
            lte_now_at_resp |= AT_LTE_RESP_GPSLOC;
#endif
        } else {
            // http post data
            if (lte_wait_at_resp & AT_LTE_RESP_HTTP_READ) {
                if (ES_NULL != http_data_cb) {
                    http_data_cb(HTTP_EVENT_BODY_DATA, hal_lte_resp_readline, (ES_U32)resp_readline_len);
                }
            }
        }
    
        es_memset(hal_lte_resp_readline, 0x00, sizeof(hal_lte_resp_readline));
    } while(1);
}

static ES_VOID hal_lte_run_at_cmd(ES_VOID)
{
    if (HAL_LTE_STATUS_POWER_ON == lte_status) {
        hal_lte_power_on_init(&pt_lte_at_send);
    } else if (HAL_LTE_STATUS_SYS_INIT == lte_status) {
        hal_lte_sys_at_init(&pt_lte_at_send);
    } else if (HAL_LTE_STATUS_NET_REG == lte_status) {
        hal_lte_net_reg_init(&pt_lte_at_send);
    } else if (HAL_LTE_STATUS_MQTT_CONN == lte_status) {
        hal_lte_mqtt_serv_conn(&pt_lte_at_send);
    } else if (HAL_LTE_STATUS_NET_READY == lte_status) {
        hal_lte_send_data(&pt_lte_at_send);
    } else if (HAL_LTE_STATUS_TRY_RESET == lte_status) {
        hal_lte_reset_init(&pt_lte_at_send);
#if ES_GPS_ENABLE
    } else if (HAL_LTE_STATUS_UPDATE_GPS == lte_status) {
        hal_lte_gps_update(&pt_lte_at_send);
#endif
    }
}

static ES_S32 hal_lte_uart_init(ES_VOID)
{
    es_uart_param_t uart_param;

	uart_param.baud = ES_4GLTE_UART_BAUD;
	uart_param.data = ES_UART_DATA_8_BIT;
	uart_param.stop = ES_UART_STOP_1_BIT;
	uart_param.parity = ES_UART_PARITY_NONE;
	uart_param.rx_cb = hal_lte_uart_rx_cb;
    if (ES_RET_SUCCESS != es_uart_open(ES_4GLTE_UART_ID, &uart_param)) {
        hal_lte_error("open uart (%d) fail", ES_4GLTE_UART_ID);
        return ES_RET_FAILURE;
    }

    return ES_RET_SUCCESS;
}

static ES_S32 hal_lte_gpio_init(ES_VOID)
{
#if ES_4GLTE_POWER_PIN
    fpioa_set_function (ES_4GLTE_POWER_PIN , FUNC_GPIOHS0 + ES_4GLTE_POWER_HS_NUM);
    gpiohs_set_drive_mode(ES_4GLTE_POWER_HS_NUM, GPIO_DM_OUTPUT);
    gpiohs_set_pin(ES_4GLTE_POWER_HS_NUM, ES_GPIO_VAL_HIGH);

    fpioa_set_function (ES_4GLTE_RESET_PIN , FUNC_GPIOHS0 + ES_4GLTE_RESET_HS_NUM);
    gpiohs_set_drive_mode(ES_4GLTE_RESET_HS_NUM, GPIO_DM_OUTPUT);
    gpiohs_set_pin(ES_4GLTE_RESET_HS_NUM, ES_GPIO_VAL_LOW);
    
    fpioa_set_function (ES_IO20_PIN , FUNC_GPIOHS0 + ES_IO20_HS_NUM);
    gpiohs_set_drive_mode(ES_IO20_HS_NUM, GPIO_DM_OUTPUT);
    gpiohs_set_pin(ES_IO20_HS_NUM, ES_GPIO_VAL_HIGH);
#endif
    return ES_RET_SUCCESS;
}

ES_S32 es_hal_lte_init(ES_VOID)
{
    ES_U8 i;

    PT_INIT(&pt_lte_at_send);
    PT_SEM_INIT(&sem_lte_at_resp, 0);
    es_memset(&lte_cell_info, 0x00, sizeof(lte_cell_info));

#if ES_GPS_ENABLE
    es_memset(&gps_info, 0x00, sizeof(es_gps_info_t));
#endif

    http_headers = es_malloc(sizeof(es_http_header_t)*ES_HTTP_HEADER_MAX_COUNT);
    if (ES_NULL == http_headers) {
        return ES_RET_NO_MEMORY;
    }
    es_memset(http_headers, 0x00, sizeof(es_http_header_t)*ES_HTTP_HEADER_MAX_COUNT);

    for (i = 0; i < LTE_AT_LIST_COUNT; i++) {
        lte_at_list[i].data_buf = es_malloc(lte_at_list[i].data_buf_len);
        if (ES_NULL == lte_at_list[i].data_buf) {
            return ES_RET_NO_MEMORY;
        }
        hal_lte_reset_at_buf(i);
    }

    rx_cirle_buf = es_circle_buf_new(LTE_UART_RX_BUF_SIZE);
    if (ES_NULL == rx_cirle_buf) {
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != hal_lte_uart_init()) {
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != hal_lte_gpio_init()) {
        return ES_RET_FAILURE;
    }

    hal_lte_debug("lte init success.");
    return ES_RET_SUCCESS;
}

ES_VOID es_hal_lte_task(ES_VOID)
{
    hal_lte_run_at_cmd();
    hal_lte_at_parser();
    hal_lte_at_send_sem_check();
}

ES_BOOL es_hal_lte_is_ready(ES_VOID)
{
    if (HAL_LTE_STATUS_NET_READY <= lte_status) {
        return ES_TRUE;
    }

    return ES_FALSE;
}

ES_S32 es_hal_lte_mqtt_send(const ES_CHAR *topic, const ES_BYTE *data, ES_U16 data_len)
{
    ES_U8 at_idx = 0;

    if (HAL_LTE_STATUS_NET_READY > lte_status) {
        return ES_RET_FAILURE;
    }

    at_idx = hal_lte_get_idle_at_buf(data_len);
    if (at_idx >= LTE_AT_LIST_COUNT) {
        hal_lte_error("no at buf");
        return ES_RET_FAILURE;
    }

    memset(lte_at_list[at_idx].data_buf, 0x00, lte_at_list[at_idx].data_buf_len);
    es_memcpy(lte_at_list[at_idx].data_buf, data, data_len);
    es_snprintf(lte_at_list[at_idx].cmd, LTE_AT_CMD_TX_LEN, "AT+QMTPUBEX=0,0,0,0,\"%s\",%d\r\n", topic, data_len);
    lte_at_list[at_idx].data_len = data_len;
    lte_at_list[at_idx].data_type = HAL_LTE_DATA_MQTT;

    return ES_RET_SUCCESS;
}

ES_S32 es_hal_lte_register_mqtt_sub_cb(es_hal_lte_mqtt_sub_cb cb)
{
    mqtt_sub_cb = cb;
    return ES_RET_SUCCESS;
}

ES_S32 es_hal_lte_http_post(const ES_CHAR *url, const ES_BYTE *data, ES_U32 data_len)
{
    ES_U8 at_idx = 0;

    if (HAL_LTE_STATUS_NET_READY > lte_status) {
        return ES_RET_FAILURE;
    }

    at_idx = hal_lte_get_idle_at_buf(data_len);
    if (at_idx >= LTE_AT_LIST_COUNT) {
        hal_lte_error("no at buf");
        return ES_RET_FAILURE;
    }
    memset(lte_at_list[at_idx].data_buf, 0x00, lte_at_list[at_idx].data_buf_len);
    es_memcpy(lte_at_list[at_idx].data_buf, data, data_len);
    es_strncpy(lte_at_list[at_idx].cmd, url, LTE_AT_CMD_TX_LEN);
    lte_at_list[at_idx].data_len = data_len;
    lte_at_list[at_idx].data_type = HAL_LTE_DATA_HTTP_POST;

    return ES_RET_SUCCESS;
}

ES_S32 es_hal_lte_http_post_jpg(const ES_CHAR *url, const es_http_header_t *headers, 
            ES_U32 header_count, const ES_BYTE *data, ES_U32 data_len)
{
    ES_U8 at_idx = 0;

    if (HAL_LTE_STATUS_NET_READY > lte_status) {
        return ES_RET_FAILURE;
    }

    at_idx = hal_lte_get_idle_at_buf(data_len);
    if (at_idx >= LTE_AT_LIST_COUNT) {
        hal_lte_error("no at buf");
        return ES_RET_FAILURE;
    }

    if (ES_NULL != headers && header_count > 0) {
        if (header_count > ES_HTTP_HEADER_MAX_COUNT) {
            header_count = ES_HTTP_HEADER_MAX_COUNT;
        }
        hal_lte_debug("header_count:%d", header_count);
        es_memset(http_headers, 0x00, sizeof(es_http_header_t)*ES_HTTP_HEADER_MAX_COUNT);
        es_memcpy(http_headers, headers, sizeof(es_http_header_t)*header_count);
    }

    memset(lte_at_list[at_idx].data_buf, 0x00, lte_at_list[at_idx].data_buf_len);
    es_memcpy(lte_at_list[at_idx].data_buf, data, data_len);
    es_strncpy(lte_at_list[at_idx].cmd, url, LTE_AT_CMD_TX_LEN);
    lte_at_list[at_idx].data_len = data_len;
    lte_at_list[at_idx].data_type = HAL_LTE_DATA_HTTP_POST;
    lte_at_list[at_idx].param = http_headers;

    return ES_RET_SUCCESS;
}

ES_S32 es_hal_lte_http_get(const ES_CHAR *url)
{
    ES_U8 at_idx = 0;

    if (HAL_LTE_STATUS_NET_READY > lte_status) {
        return ES_RET_FAILURE;
    }

    at_idx = hal_lte_get_idle_at_buf(0);
    if (at_idx >= LTE_AT_LIST_COUNT) {
        hal_lte_error("no at buf");
        return ES_RET_FAILURE;
    }

    es_strncpy(lte_at_list[at_idx].cmd, url, LTE_AT_CMD_TX_LEN);
    lte_at_list[at_idx].data_len = 0;
    lte_at_list[at_idx].data_type = HAL_LTE_DATA_HTTP_GET;

    return ES_RET_SUCCESS;
}


ES_S32 es_hal_lte_register_http_cb(es_hal_lte_data_cb cb)
{
    http_data_cb = cb;
    return ES_RET_SUCCESS;
}

ES_S32 es_hal_lte_get_mac(ES_BYTE *mac)
{
    ES_U8 i = 0;
    ES_U8 j = 0;

    if (0 == hal_lte_imei[0]) {
        return ES_RET_FAILURE;
    }

    for (i = 0; i < 6; i++) {
        // just last 12 char, so start with 3.
        j = 3+i*2;
        mac[i] = (((hal_lte_imei[j]-'0')<<4)&0xF0) | ((hal_lte_imei[j+1]-'0')&0x0F);
    }

    return ES_RET_SUCCESS;
}

// ref ES_MAC_COLON_STR_LEN
// ref ES_MQTT_CFG_DEV_MAC_FMT
ES_S32 es_hal_lte_get_mqtt_id(ES_CHAR *mqtt_id)
{
    uint8_t mac[6];

    if (ES_RET_SUCCESS != es_hal_lte_get_mac(mac)) {
        return ES_RET_FAILURE;
    }

    es_snprintf((char *)mqtt_id, ES_MAC_COLON_STR_LEN, ES_MQTT_CFG_DEV_MAC_FMT,
        mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);

    return ES_RET_SUCCESS;
}


const es_hal_lte_cell_t *es_hal_lte_get_cell_info(ES_VOID)
{
    if (0 == lte_cell_info.ci[0] || 0 == lte_cell_info.lac[0]) {
        return ES_NULL;
    }

    return (const es_hal_lte_cell_t * )&lte_cell_info;
}

const ES_CHAR *es_hal_lte_get_iccid(ES_VOID)
{
    if (0 == hal_lte_iccid[0]) {
        return ES_NULL;
    }

    return (const ES_CHAR *)hal_lte_iccid;
}

const ES_CHAR *es_hal_lte_get_imei(ES_VOID)
{
    if (0 == hal_lte_imei[0]) {
        return ES_NULL;
    }

    return (const ES_CHAR *)hal_lte_imei;
}

ES_U8 es_hal_lte_get_rssi(ES_VOID)
{
    return hal_lte_rssi;
}

ES_S32 es_hal_lte_get_gps(ES_VOID *gps)
{
#if ES_GPS_ENABLE
    // ES_S32 len = 0;
    if (0 == gps_info.latitude[0] || 0 == gps_info.longitude[0]) {
        return es_spec_car_get_gps_from_cache(gps);
    }

    // len += es_sprintf(gps_json,"{\"latitude\":%s,\"longitude\":%s,\"hdop\":%s,\"altitude\":%s,\"fix\":%s,\"cog\":%s,\"spkm\":%s,\"spkn\":%s,\"nsat\":%s}",
    //     gps_info.latitude, gps_info.longitude,
    //     gps_info.hdop, gps_info.altitude,
    //     gps_info.fix, gps_info.cog,
    //     gps_info.spkm, gps_info.spkn,
    //     gps_info.nsta);
    // return len;
    es_memcpy((es_gps_info_t *)gps, &gps_info, sizeof(es_gps_info_t));
    return ES_RET_SUCCESS;

#else
    return ES_RET_FAILURE;
#endif
}

#endif

