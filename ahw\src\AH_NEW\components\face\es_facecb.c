#include "es_inc.h"
#include "facelib_inc.h"
#if ES_FACE_UPLOAD_ENABLE
#include "jpeg_encode.h"
#endif

#define ES_FACECB_DEBUG
#ifdef ES_FACECB_DEBUG
#define es_facecb_debug es_log_info
#define es_facecb_error es_log_error
#else
#define es_facecb_debug(...)
#define es_facecb_error(...)
#endif


#define ES_FACECD_SAME_UID_TIMEOUT_SEC          (5)
#if ES_FACE_UPLOAD_ENABLE
#define ES_FACE_PIC_JPEG_ENCODE_BUF_LEN         (20 * 1024)
#define ES_FACE_PIC_JPEG_ENCODE_QUALITY         (30) /* 1-34, 35-59, 60+ */
#endif

#define FACECD_DETECT_FAIL_MAX_COUNT            (5)
#define FACECD_DETECT_FAIL_TIMEOUT_MS           (10*1000)
typedef struct {
    ES_U32 fail_count;
    ES_U32 detect_time_ms;
} facecb_detect_fail_t;

static ES_U32 face_detect_time = 0;
static ES_U8 last_uid[FACEDB_UID_LEN] = {0};
static ES_U8 driver_uid[FACEDB_UID_LEN] = {0};
static ES_U32 last_uid_time = 0;
static ES_U32 last_pass_time = 0;
static facecb_detect_fail_t facecb_detect_fail = {0, 0};
static ES_BOOL is_first_boot_recognition = ES_TRUE;  // 开机初次识别通过标志

// #if ES_FACE_UPLOAD_ENABLE
// #if CONFIG_APP_WITH_OTA
//     /* max_size JPEG_ENCODE_BUF_LEN */
//     static uint8_t *face_pic_jpeg_encode_buf = (uint8_t*)(uintptr_t)0x80000000;
// #else
//     static uint8_t face_pic_jpeg_encode_buf[ES_FACE_PIC_JPEG_ENCODE_BUF_LEN];
// #endif /* CONFIG_APP_WITH_OTA */
// #endif


// #ifdef ES_FACECB_DEBUG
// static ES_VOID es_facecb_print_info(face_obj_t *face_obj)
// {
//     dbf_item_t *item = ES_NULL;
// 	dbmeta_t *meta = ES_NULL;

//     if (ES_NULL == face_obj) {
//         es_facecb_debug("face_obj is NULL");
//         return;
//     }
//     es_facecb_debug("score:%d", (int)face_obj->score);

//     item = (dbf_item_t*)mf_facedb.item_buf;
//     if (ES_NULL == item) {
//         es_facecb_debug("item is NULL");
//         return;
//     }
//     es_facecb_debug("index:%d,uid:%s", item->index, item->uid);
// }
// #endif


#if ES_PASSLOG_ENABLE
static ES_VOID es_facecb_add_passlog(const ES_U8 *uid, ES_U32 timestamp)
{
    es_passlog_t passlog;

    // network is ok
    if (ES_NETWORK_CONNECTED == es_network_get_status()) {
        return;
    }

    es_memset(&passlog, 0x00, sizeof(passlog));
    passlog.timestamp = timestamp;
    es_memcpy(passlog.uid, uid, ES_PASSLOG_UID_LEN);
    es_passlog_write(&passlog);
    es_facecb_debug("passlog count:%d", es_passlog_get_count());
}
#endif

static ES_BOOL es_facecb_is_same_uid(const ES_U8 *uid)
{
    ES_U32 now_time;

    now_time = es_time_get_timestamp();
    if (now_time - last_uid_time < 4) {
        return ES_TRUE;
    }

    if (0 != es_memcmp(last_uid, uid, FACEDB_UID_LEN)) {
        es_memcpy(last_uid, uid, FACEDB_UID_LEN);
        goto NOT_SAME;
    }

    if (now_time - last_uid_time > ES_FACECD_SAME_UID_TIMEOUT_SEC) {
        goto NOT_SAME;
    }

    return ES_TRUE;

NOT_SAME:
    last_uid_time = now_time;

    return ES_FALSE;
}

// #define NOWTIME (1668614400) /* 2022-11-17 00:00:00 */
// // check allowed through, 
// static ES_BOOL es_facecb_allowed_through(const dbf_item_t *item)
// {
//     const dbmeta_t* meta  = ES_NULL;
//     ES_U32 now_timestamp = 0;

//     meta = (dbmeta_t*)item->meta;
//     if (ES_NULL == meta) {
//         return ES_FALSE;
//     }

//     es_facecb_debug("pass, start_time:%d, end_time:%d", meta->start_time, meta->end_time);

//     if (0 == meta->start_time && 0 == meta->end_time) {
//         return ES_TRUE;
//     }

//     now_timestamp = es_time_get_timestamp();
//     if (now_timestamp < meta->start_time || meta->end_time < now_timestamp || now_timestamp < NOWTIME) {
//         return ES_FALSE;
//     }

//     return ES_TRUE;
// }

static ES_BOOL es_facecb_check_vaild_uid(face_obj_t *face_obj)
{
    dbf_item_t *item = ES_NULL;
    item = (dbf_item_t*)mf_facedb.item_buf;
    ES_S32 vid = 0;

    if (0 == (int)face_obj->score) {
        return ES_FALSE;
    }

    vid = mf_facedb.uid2vid(item->uid);
    if (vid == -1) {
        return ES_FALSE;
    }

    return ES_TRUE;
}

static void es_facecb_upload_log(face_obj_t *face_obj, ES_U32 timestamp)
{
    dbf_item_t *item = ES_NULL;
	dbmeta_t *meta = ES_NULL;

    item = (dbf_item_t*)mf_facedb.item_buf;
    meta = (dbmeta_t*)item->meta;

// #if ES_RELAY_ENABLE
//     es_relay_open((ES_U16)(mf_brd.cfg.relay.opent*100));
// #endif

#if ES_DOOR_ENABLE
    es_door_open((ES_U16)0);
#endif

// #if ES_AUDIO_ENABLE
//     // es_audio_play(ES_AUDIO_PLAY_PASS);
//     es_voice_play(ES_VOICE_01_FACE_PASS);
// #endif

#if ES_PASSLOG_ENABLE
    es_facecb_add_passlog(item->uid, timestamp);
#endif

    es_network_upload_pass_log(item->uid, timestamp, ES_NETWORK_PASS_BY_FACE, (ES_U32)meta->end_time, (ES_U32)meta->start_time);
}

// #if ES_FACE_UPLOAD_ENABLE
#if 0
#if ES_CAM_DIR 
// vertical
#define JPEG_SRC_WIDTH      (240)
#define JPEG_SRC_HEIGHT     (320)
#else
#define JPEG_SRC_WIDTH      (320)
#define JPEG_SRC_HEIGHT     (240)
#endif

static void es_facecb_upload_pic(face_obj_t *face_obj, ES_U32 timestamp, ES_U8 type)
{
    jpeg_encode_t jpeg_src, jpeg_out;
    uint8_t *cam_buf = ES_NULL;

    cam_buf = mf_cam.rgb_image[mf_cam.rgb_buf_index];
    jpeg_src.w = JPEG_SRC_WIDTH;
    jpeg_src.h = JPEG_SRC_HEIGHT;
    jpeg_src.bpp = 2;
    jpeg_src.data = cam_buf;

    jpeg_out.w = JPEG_SRC_WIDTH;
    jpeg_out.h = JPEG_SRC_HEIGHT;
    jpeg_out.bpp = ES_FACE_PIC_JPEG_ENCODE_BUF_LEN;
    jpeg_out.data = face_pic_jpeg_encode_buf;

    convert_rgb565_order((uint16_t*)cam_buf, JPEG_SRC_WIDTH, JPEG_SRC_HEIGHT);
    if (jpeg_compress(&jpeg_src, &jpeg_out, ES_FACE_PIC_JPEG_ENCODE_QUALITY, 0) != 0) {
        convert_rgb565_order((uint16_t*)cam_buf, JPEG_SRC_WIDTH, JPEG_SRC_HEIGHT);
        es_ui_cam_update(ES_TRUE);
        es_facecb_debug("jpeg compress error");
        return;
    }
    convert_rgb565_order((uint16_t*)cam_buf, JPEG_SRC_WIDTH, JPEG_SRC_HEIGHT);
    es_ui_cam_update(ES_TRUE);

    es_facecb_debug("jpeg length:%d", jpeg_out.bpp);
    es_ntework_upload_pic((const ES_BYTE *)jpeg_out.data, jpeg_out.bpp, timestamp, type);
}
#endif

static void es_facecb_detect(face_obj_t *face_obj, uint8_t i, uint8_t n)
{
//     es_facecb_debug("##Det Face: x1:%d y1:%d x2:%d y2:%d\r\n", face_obj->x1, face_obj->y1, face_obj->x2, face_obj->y2);
//     es_facecb_debug("es_facecb_detect");
// #ifdef ES_FACECB_DEBUG
//     es_facecb_print_info(face_obj);
// #endif
    ES_U32 now_time = 0;
    ES_U32 timestamp = 0;
    static ES_U32 last_upload_time = 0;
    facecb_detect_fail.fail_count++;

    now_time = es_time_get_sytem_ms();
    if (0 == facecb_detect_fail.detect_time_ms || 
        ((now_time-facecb_detect_fail.detect_time_ms) > FACECD_DETECT_FAIL_TIMEOUT_MS)) {
        facecb_detect_fail.detect_time_ms = now_time;
        facecb_detect_fail.fail_count = 0;
    }

    es_facecb_update_detect_time();
    if (facecb_detect_fail.fail_count < FACECD_DETECT_FAIL_MAX_COUNT) {
        es_ui_face_edge(face_obj->x1, face_obj->y1, face_obj->x2, face_obj->y2, ES_FACECB_DETECT);
        return;
    }

    es_ui_face_edge(face_obj->x1, face_obj->y1, face_obj->x2, face_obj->y2, ES_FACECB_STRANGER);
    facecb_detect_fail.fail_count = 0;

    timestamp = es_time_get_timestamp();
    if ((timestamp - last_upload_time) < 5) {
        return;
    }
    last_upload_time = timestamp;
#if ES_FACE_UPLOAD_ENABLE
    // printk("heap:%ld KB\r\n", get_free_heap_size() / 1024);
    if (ES_NETWORK_CONNECTED == es_network_get_status()) {
        // es_facecb_upload_pic(face_obj, timestamp, ES_FACECB_STRANGER);
        if (is_first_boot_recognition) {
            es_capture_upload_pic(timestamp, ES_FACECB_STRANGER);
        } else {
            // 中途识别失败，不上报图片，上报AI事件20（驾驶员姿态检测）
            es_network_mqtt_upload_ai_event(AI_EVENT_POSTURE_DETECTION, 1, ES_NULL, ES_NULL);
        }
    }
#endif
}

static int last_play_time = 0;
static void es_facecb_stranger(face_obj_t *face_obj, uint8_t i, uint8_t n)
{
//     es_facecb_debug("es_facecb_stranger");
// #ifdef ES_FACECB_DEBUG
//     es_facecb_print_info(face_obj);
// #endif

    if (es_facecb_check_vaild_uid(face_obj)) {
        es_ui_face_edge(face_obj->x1, face_obj->y1, face_obj->x2, face_obj->y2, ES_FACECB_DETECT);
    } else {
        es_ui_face_edge(face_obj->x1, face_obj->y1, face_obj->x2, face_obj->y2, ES_FACECB_STRANGER);
    }
    if (es_time_get_sytem_ms() - last_play_time > 5000) {
        es_voice_play(ES_VOICE_05_NOT_AUTH_AND_LEAVE);
        last_play_time =  es_time_get_sytem_ms();
    }
    es_facecb_update_detect_time();

}

static void es_facecb_fake(face_obj_t *face_obj, uint8_t i, uint8_t n)
{
//     es_facecb_debug("es_facecb_fake");
// #ifdef ES_FACECB_DEBUG
//     es_facecb_print_info(face_obj);
// #endif
    // if (es_facecb_check_vaild_uid(face_obj)) {
    //     es_ui_face_edge(face_obj->x1, face_obj->y1, face_obj->x2, face_obj->y2, ES_FACECB_DETECT);
    // } else {
    //     es_ui_face_edge(face_obj->x1, face_obj->y1, face_obj->x2, face_obj->y2, ES_FACECB_FAKE);
    // }
    // es_facecb_update_detect_time();
}

#define FACECB_BASE_TIME (1668614400) /* 2022-11-17 00:00:00 */
static ES_BOOL es_facecb_check_car_allowed_boot(ES_U32 timestamp)
{
    if (timestamp < FACECB_BASE_TIME) {
        es_facecb_debug("timestamp is not sync");
        return ES_FALSE;
    }

    if (ES_TRUE == es_spec_car_is_lock()) {
        es_facecb_debug("car is locked");
        return ES_FALSE;
    }

    if ((timestamp > FACECB_BASE_TIME) && (es_spec_car_get_expire_time() < timestamp)) {
        es_facecb_debug("car is expire");
        return ES_FALSE;
    }

    return ES_TRUE;
}


static ES_BOOL es_facecb_check_driver_allowed(const dbf_item_t *item, ES_U32 timestamp)
{
    const dbmeta_t* meta  = ES_NULL;

    meta = (dbmeta_t*)item->meta;
    if (ES_NULL == meta) {
        return ES_FALSE;
    }

    es_facecb_debug("pass, start_time:%d, end_time:%d", meta->start_time, meta->end_time);
    if (0 == meta->start_time && 0 == meta->end_time) {
        es_facecb_debug("not set time expire");
        return ES_TRUE;
    }

    if (timestamp < meta->start_time || meta->end_time < timestamp) {
        es_facecb_debug("driver is expired");
        es_voice_play(ES_VOICE_12_DRIVER_LICENSE_EXPIRE);
        return ES_FALSE;
    }

    return ES_TRUE;
}


static void es_facecb_pass(face_obj_t *face_obj, uint8_t i, uint8_t n)
{
    dbf_item_t *item = ES_NULL;
    ES_U32 timestamp = 0;
	// dbmeta_t *meta = ES_NULL;

    facecb_detect_fail.fail_count = 0;
    timestamp = es_time_get_timestamp();
    if (!es_facecb_check_car_allowed_boot(timestamp)) {
        if (1 == mf_brd.cfg.lock) {
            es_voice_play(ES_VOICE_16_CAR_LOCKED);
        } else if (2 == mf_brd.cfg.lock) {
            es_voice_play(ES_VOICE_14_EXPIRE_AND_LOCKED);
        }
        return;
    }

    item = (dbf_item_t*)mf_facedb.item_buf;
    es_facecb_update_detect_time();
    // meta= (dbmeta_t*)item->meta;

    if (mf_facedb.get_vid(face_obj->index, mf_facedb.item_buf) != MF_ERR_NONE) {	
        es_facecb_error("face obj index error!\r\n");
        return;
    }

    if ((timestamp - last_pass_time) < 5) { //5秒内识别通过一次，防止连续识别
        return;
    }
    last_pass_time = timestamp;

    // check driver info
    if (!es_facecb_check_driver_allowed(item, timestamp)) {
        return;
    }
    es_ui_face_edge(face_obj->x1, face_obj->y1, face_obj->x2, face_obj->y2, ES_FACECB_PASS);

    if (0 == driver_uid[0]) {
        es_memcpy(driver_uid, item->uid, FACEDB_UID_LEN);
#if ES_RELAY_ENABLE
        es_relay_open_always();
#endif
        es_voice_play(ES_VOICE_06_AUTH_PASS);
    } else {
        es_voice_play(ES_VOICE_01_FACE_PASS);
    }
    es_spec_car_set_driver_pass();

    // if (es_facecb_allowed_through(item)) {
    //     es_ui_face_edge(face_obj->x1, face_obj->y1, face_obj->x2, face_obj->y2, ES_FACECB_PASS);
    // } else {
    //     es_ui_face_edge(face_obj->x1, face_obj->y1, face_obj->x2, face_obj->y2, ES_FACECB_AUTH_FAIL);
    //     return;
    // }

    // es_facecb_debug("es_facecb_pass");
    if (es_facecb_is_same_uid(item->uid)) {
        es_facecb_debug("same face, too fast");
        return;
    }

    if (is_first_boot_recognition) {
    // 初次识别成功后上报记录，清除标志位，中途识别成功，不再上报记录、图片
    es_facecb_upload_log(face_obj, timestamp);
#if ES_FACE_UPLOAD_ENABLE
    // printk("heap:%ld KB\r\n", get_free_heap_size() / 1024);
    if (ES_NETWORK_CONNECTED == es_network_get_status()) {
        if (0 == es_memcmp(driver_uid, item->uid, FACEDB_UID_LEN)) {
            // es_facecb_upload_pic(face_obj, timestamp, ES_FACECB_PASS);
            es_capture_upload_pic(timestamp, ES_FACECB_PASS);
        } else {
            // es_facecb_upload_pic(face_obj, timestamp, ES_FACECB_AUTH_FAIL);
            es_capture_upload_pic(timestamp, ES_FACECB_PASS);
        }
    }
#endif
        is_first_boot_recognition = ES_FALSE;
    }
}

static void es_facecb_pre_display(void)
{
}

static void es_facecb_post_display(void)
{
}

mf_facecb_t es_facecb = {
    .detect = es_facecb_detect,
    .stranger = es_facecb_stranger,
    .fake = es_facecb_fake,
    .pass = es_facecb_pass,
    .pre_display = es_facecb_pre_display,
    .post_display = es_facecb_post_display
};


ES_U32 es_facecb_get_detect_time_ms(ES_VOID)
{
    return face_detect_time;
}

ES_VOID es_facecb_update_detect_time(ES_VOID)
{
    face_detect_time = es_time_get_sytem_ms();
}