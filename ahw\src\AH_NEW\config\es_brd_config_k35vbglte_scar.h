/*
** Copyright (c) 2016-2020 lines<<EMAIL>>
** file: es_utils.h
** bef: define the interface for configure 
** auth: lines<<EMAIL>>
** create on 2020.06.27 
*/

#ifndef _ES_BRD_CONFIG_K35VWTY_H_
#define _ES_BRD_CONFIG_K35VWTY_H_

#ifdef __cplusplus 
extern "C" { 
#endif

// Col is 96
/////////////////////////////////////// system info ///////////////////////////////////////////
#define ES_SYSTEM_INFO
#define ES_SYS_SVER_VAL                     (20035)
#define ES_SYS_SVER_STR                     ("2.0.35")
#define ES_SYS_HVER_VAL                     (10000)
#define ES_SYS_HVER_STR                     ("1.0.0")


/////////////////////////////////////// 4G LTE ////////////////////////////////////////////////
#define ES_4GLTE_ENABLE                     (1)
#define ES_4GLTE_UART_ID                    (ES_UART_ID_0)
#define ES_4GLTE_UART_BAUD                  (ES_UART_BAUD_115200)
#define ES_4GLTE_MODULE_TYPE                (ES_4GLTE_MODULE_EC800X)
#define ES_4GLTE_POWER_PIN                  (19)
#define ES_4GLTE_POWER_HS_NUM               (28)
#define ES_IO20_PIN                         (20)
#define ES_IO20_HS_NUM                      (29)
#define ES_4GLTE_RESET_PIN                  (8)
#define ES_4GLTE_RESET_HS_NUM               (30)


/////////////////////////////////////// BLE Module ////////////////////////////////////////////
#define ES_BLE_MODULE_ENABLE                (0)
#define ES_BLE_HAL_TYPE                     (ES_BLE_HAL_571)
#define ES_BLE_UART_ID                      (ES_UART_ID_1)
#define ES_BLE_PAYLOAD_GPIO_PIN             (26)
#define ES_BLE_PAYLOAD_GPIO_HS_NUM          (29)
#define ES_BLE_PAYLOAD_DATA_LEN             (32)


/////////////////////////////////////// LCD ///////////////////////////////////////////////////
#define ES_LCD_DRIVER_TYPE                  (ES_LCD_DRIVER_ST7789)
#define ES_LCD_DCX_HS_NUM                   (5)
#define ES_LCD_RST_HS_NUM                   (6)
#define ES_LCD_BL_HS_NUM                    (16)
#define ES_LCD_SPI_CS_HS_NUM                (17)
#define ES_LCD_CS_PIN                       (36)
#define ES_LCD_RST_PIN                      (37)
#define ES_LCD_DCX_PIN                      (38)
#define ES_LCD_SCK_PIN                      (39)
#define ES_LCD_BL_PIN                       (17)
#define ES_LCD_PWM_DEV_BL                   (PWM_DEVICE_0)
#define ES_LCD_PWDM_CHN_BL                  (PWM_CHANNEL_1)
#define ES_LCD_DIR                          (1) // 0:horizontal, 1:vertical
#define ES_LCD_HMIRROR                      (0)
#define ES_LCD_VFLIP                        (0)
#define ES_LCD_XY_SWAP                      (0)
#define ES_LCD_DIR_PARAM                    ((ES_LCD_HMIRROR<<7) | (ES_LCD_VFLIP<<6) | (ES_LCD_XY_SWAP<<5))
#define ES_LCD_WIDTH                        (240)
#define ES_LCD_HEIGHT                       (320)


/////////////////////////////////////// UI ////////////////////////////////////////////////////
#define ES_UI_WIDTH                         (ES_LCD_WIDTH)
#define ES_UI_HEIGHT                        (ES_LCD_HEIGHT)
#define ES_UI_CAM_WIDTH                     (240)
#define ES_UI_CAM_HEIGHT                    (320)
#define ES_UI_FACE_EDGE_LINE_WIDTH          (2) // pixel
#define ES_UI_FACE_EDGE_LINE_LENGTH         (10) // pixel
#define ES_UI_TYPE                          (ES_UI_TYPE_K35V_240_320_GPS)


/////////////////////////////////////// Camera ////////////////////////////////////////////////
#define ES_CAM_WIDTH                        (240)
#define ES_CAM_HEIGHT                       (320)
#define ES_CAM_DIR                          (1) // direction, 0:horizontal, 1:vertical
#define ES_CAM_HMIRROR                      (1) // hmirror
#define ES_CAM_VFLIP                        (0)// vflip
#define ES_CAM_EXP_TIME                     (128)



/////////////////////////////////////// UART //////////////////////////////////////////////////
#define ES_UART_MODULE_ENABLE               (1)
#define ES_UART0_RX_PIN                     (7)
#define ES_UART0_TX_PIN                     (6)
#define ES_UART1_RX_PIN                     (27)
#define ES_UART1_TX_PIN                     (26)
#define ES_UART2_RX_PIN                     (4)
#define ES_UART2_TX_PIN                     (5)


/////////////////////////////////////// KEY ///////////////////////////////////////////////////
#define ES_KEY_MODULE_ENABLE                (0)
#define ES_KEY1_GPIO_PIN                    (8)
#define ES_KEY1_GPIO_HS_NUM                 (0)
#define ES_KEY1_PRESS_VAL                   (0)


/////////////////////////////////////// file system ///////////////////////////////////////////
#define ES_FS_ENABLE                        (0)


/////////////////////////////////////// FACE //////////////////////////////////////////////////
#define ES_FACE_UPLOAD_ENABLE               (1)


/////////////////////////////////////// PASS LOG //////////////////////////////////////////////
#define ES_PASSLOG_ENABLE                   (1)
#define ES_PASSLOG_HDR_MAGIC                (0x12345678)
#define ES_PASSLOG_CACHE_COUNT              (8)
#define ES_PASSLOG_JSON_STR_LEN             (96)
#define ES_PASSLOG_UID_LEN                  (32)


/////////////////////////////////////// DOOR //////////////////////////////////////////////////
#define ES_DOOR_ENABLE                      (0)


/////////////////////////////////////// MODEL /////////////////////////////////////////////////
#define ES_MODEL_ACTIVE_ENABLE              (1)
#define ES_MODEL_ACTIVE_PROTO_DATA_LEN      (256)
#define ES_MODEL_ACTIVE_KEY_LEN             (32)


/////////////////////////////////////// OTA ///////////////////////////////////////////////////
#define ES_OTA_DL_ENABLE                    (1)


/////////////////////////////////////// GPS ///////////////////////////////////////////////////
#define ES_GPS_ENABLE                       (1)


/////////////////////////////////////// CAPTURE ///////////////////////////////////////////////
#define ES_CAPTURE_ENABLE                   (1)
#define ES_CAPTURE_MIN_INTERVAL_SEC         (1)
#define ES_CAPTURE_MAX_INTERVAL_SEC         (60*60)

/////////////////////////////////////// MQTT //////////////////////////////////////////////////
#define ES_MQTT_ENABLE                      (1)
#define ES_MQTT_CFG_IP                      ("**************")
// #define ES_MQTT_CFG_IP                      ("************")//公司服务器
#define ES_MQTT_CFG_PORT                    (1883)
#define ES_MQTT_CFG_USERNAME                ("km")
#define ES_MQTT_CFG_PASSWD                  ("123")
#define ES_MQTT_CFG_DEV_MAC_FMT             ("%02X%02X%02X%02X%02X%02X")
#define ES_MQTT_CFG_DEV_SUB_TOPIC_FMT       ("forklift/%s")
#define ES_MQTT_CFG_DEV_PUB_TOPIC           ("forklift/server")
#define ES_MQTT_HEART_TIME_SEC              (60)
#define ES_MQTT_UPLOAD_STATUS_TIME_SEC      (30)


/////////////////////////////////////// RTC ///////////////////////////////////////////////////
#define ES_RTC_ENABLE                       (1)


/////////////////////////////////////// APP SPEC CAR //////////////////////////////////////////
#define ES_ENABLE_SPEC_CAR                  (1)
#define ES_SAFETYBELT_PIN                   (32)
#define ES_SAFETYBELT_HS_NUM                (31)
#define ES_SAFETYBELT_INSERT_VAL            (0) // 客户默认常闭，0表示常闭

#define AI_EVENT_FACE_PASS                  (0)  /* 0 验证通过, 人脸识别通过 */
#define AI_EVENT_FACE_FAIL                  (1)  /* 1 陌生人-未授权, 人脸识别失败 */
#define AI_EVENT_FACE_FAKE                  (2)  /* 2 非活体假人 */
#define AI_EVENT_FACE_CAP                   (3)  /* 3 抓拍图片 */
#define AI_EVENT_FACE_FAIL_HALFWAY          (4)  /* 4 授权失败（中途人脸匹配不成功） */
#define AI_EVENT_SOMKING                    (11) /* 11 抽烟 */
#define AI_EVENT_DISTRACTED                 (12) /* 12 疲劳驾驶 */
#define AI_EVENT_CALLING                    (13) /* 13 打电话 */
#define AI_EVENT_NOT_SEAT_BELT              (14) /* 14 未系安全带 */
#define AI_EVENT_NOT_SAFETY_HELMET          (15) /* 15 未戴安全帽 */
#define AI_EVENT_OVER_SPEED                 (16) /* 16 超速 */
#define AI_EVENT_DRINKING                   (17) /* 17 开车途中喝水 */
#define AI_EVENT_FRONT_HAS_PEDESTRIAN       (18) /* 18 车前盲区有人 */
#define AI_EVENT_BACK_HAS_PEDESTRIAN        (19) /* 19 车后盲区有人 */
#define AI_EVENT_POSTURE_DETECTION          (20) /* 20 驾驶员姿态检测 */

#ifdef __cplusplus 
}
#endif
#endif