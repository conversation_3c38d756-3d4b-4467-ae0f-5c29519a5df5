#include "es_inc.h"
#include "facelib_inc.h"

#if ES_ENABLE_SPEC_CAR

#define ES_SPEC_CAR_DEBUG
#ifdef ES_SPEC_CAR_DEBUG
#define es_spec_car_debug es_log_info
#define es_spec_car_error es_log_error
#else
#define es_spec_car_debug(...)
#define es_spec_car_error(...)
#endif

#define ES_SPEC_CAR_BASE_TIMESTAMP          (1683820800) // 2023.5.12 00:00:00
#define ES_SPEC_CAR_MONTH_SEC               (60*60*24*30L)


static ES_U32 car_lock = 0;
static ES_U32 car_expire_time = 0;
static ES_U32 emergency = 0;
static ES_BOOL driver_pass = ES_FALSE;
static ES_U32 last_face_flow_time = 0;
static ES_CHAR gps_cache_str[LTE_GPS_CACHE_STR_LEN] = {0};
static ES_BOOL gps_cache_update = ES_FALSE;

static ES_VOID es_spec_car_try_notify(ES_VOID)
{
    static ES_U32 notify_time = 0;

    if (!driver_pass) {
        return;
    }

    if (ES_RET_SUCCESS != es_time_check_timeout_ms(&notify_time, 60*1000)) {
        return;
    }

    if (0 == mf_brd.cfg.working_face_detect) {
        return;
    }

    if (es_spec_car_is_face_flow()) {
        es_voice_play(ES_VOICE_04_LOOK_AND_CHECK);
    }
}

static ES_VOID es_spec_car_save_gps(ES_VOID)
{
    static ES_U32 last_save_time = 0;
    ES_U32 now_time;
    es_task_param_t task_param;

    if (!gps_cache_update) {
        return;
    }

    now_time = es_time_get_timestamp();
    if (0 == last_save_time) {
        last_save_time = now_time;
        return;
    }

    if (now_time - last_save_time < 5*60) {
        return;
    }

    strncpy( mf_brd.cfg.gps_cache_str, gps_cache_str, LTE_GPS_CACHE_STR_LEN);
    es_log_info("mf_brd.cfg.gps_cache_str=%d", gps_cache_str[0]);
    task_param.type = ES_TASK_SAVE_BRD_CFG;
    task_param.param = ES_NULL;
    task_param.timeout = 0;
    es_task_queue_push_wait(&task_param);
    gps_cache_update = ES_FALSE;
}

static ES_VOID es_spec_car_check_no_body(ES_VOID)
{
    //
#define ES_SPEC_CAR_NO_BODY_TIMEOUT_MS          (30 * 1000)
    static ES_U32 last_check_timestamp = 0;
    ES_U32 now_timestamp;
    ES_U32 now_sys_ms = 0;

    if (0 == mf_brd.cfg.posture_detect) {
        return;
    }

    now_timestamp = es_time_get_timestamp();
    if (0 == last_check_timestamp) {
        last_check_timestamp = now_timestamp;
        return;
    }

    now_sys_ms = es_time_get_sytem_ms();
    if (!es_utils_check_timeout(es_facecb_get_detect_time_ms(), now_sys_ms, ES_SPEC_CAR_NO_BODY_TIMEOUT_MS)) {
        return;
    }


    // 防止无符号整数下溢
    if (now_timestamp < last_check_timestamp || (now_timestamp - last_check_timestamp < 15*60)) {
        return;
    }
    last_check_timestamp = now_timestamp;

    es_voice_play(ES_VOICE_22_POSTURE_DETECTION);
    es_network_mqtt_upload_ai_event(AI_EVENT_POSTURE_DETECTION, 1, ES_NULL, ES_NULL);

}

ES_S32 es_spec_car_init(ES_VOID)
{
    car_lock = mf_brd.cfg.lock;
    car_expire_time = mf_brd.cfg.car_expire;
    emergency = mf_brd.cfg.emergency;
    es_log_info("mf_brd.cfg.gps_cache_str=%d", gps_cache_str[0]);
    es_log_info("mf_brd.cfg.gps_cache_str=%s", gps_cache_str[0]);
    if (0 < mf_brd.cfg.gps_cache_str[0] && mf_brd.cfg.gps_cache_str[0] < 200) {
        strncpy(gps_cache_str, mf_brd.cfg.gps_cache_str, LTE_GPS_CACHE_STR_LEN);
        es_log_info("gps_cache_str=%d", gps_cache_str[0]);
    }
    return ES_RET_SUCCESS;
}

ES_VOID es_spec_car_run(ES_VOID)
{
    ES_U32 now_time;
    static ES_BOOL is_boot_check = ES_FALSE;

    // if (is_boot_check) {
    //     return;
    // }

    // 1 check network is ok
    // if (ES_NETWORK_CONNECTED != es_network_get_status()) {
    //     return;
    // }


    now_time = es_time_get_timestamp();
    if (now_time < ES_SPEC_CAR_BASE_TIMESTAMP) {
        return;
    }

    es_spec_car_save_gps();

    es_spec_car_try_notify();

    es_spec_car_check_no_body();

    if (!is_boot_check) {
        // 2 check car is lock
        if (1 == car_lock) {
            // lock screen and play voice
            es_task_queue_play_voice(ES_VOICE_16_CAR_LOCKED);
        } else if (2 == car_lock) {
            // lock screen and play voice
            es_task_queue_play_voice(ES_VOICE_14_EXPIRE_AND_LOCKED);
        } else {

            // 3 如果处于紧急制动，则直接打开
            if (es_spec_car_is_emergency()) {
                es_relay_open_always();
            }

            // 设置车辆过期时间
            if (0 != car_expire_time) {
                // 4 check expire time
                if (now_time >= car_expire_time) {
                    /* 该设备已超期，已锁定 */
                    car_lock = 1;
                    es_task_queue_play_voice(ES_VOICE_14_EXPIRE_AND_LOCKED);
                } else if ((car_expire_time - now_time) < ES_SPEC_CAR_MONTH_SEC) {
                    es_task_queue_play_voice(ES_VOICE_13_CAR_EXPIRE_AFTER_MONTH);
                }
            // } else {
            //     es_task_queue_play_voice(ES_VOICE_02_CAR_BOOT);
            }
        }

        is_boot_check = ES_TRUE;

        if (!es_spec_car_get_driver_pass()) {
            return;
        }
    }
}


ES_BOOL es_spec_car_is_lock(ES_VOID)
{
    if (0 != car_lock) {
        return ES_TRUE;
    }

    return ES_FALSE;
}

ES_U32 es_spec_car_get_expire_time(ES_VOID)
{
    if (0 == car_expire_time) {
        return (es_time_get_timestamp()+999);
    }
    return car_expire_time;
}

ES_BOOL es_spec_car_is_emergency(ES_VOID)
{
    if (0 == emergency) {
        return ES_FALSE;
    }

    return ES_TRUE;
}

ES_U32 es_spec_car_set_driver_pass(ES_VOID)
{
    driver_pass = ES_TRUE;
    last_face_flow_time = es_time_get_sytem_ms();
    return ES_RET_SUCCESS;
}

ES_BOOL es_spec_car_get_driver_pass(ES_VOID)
{
    return driver_pass;
}

ES_BOOL es_spec_car_is_face_flow(ES_VOID)
{
    ES_U32 now_time;

    if (es_spec_car_is_emergency()) {
        return ES_FALSE; 
    }

    if (!driver_pass) {
        return ES_TRUE;
    }

    now_time = es_time_get_sytem_ms();
    if ((now_time - last_face_flow_time) > (10*60*1000*18)) { //人脸中途识别3小时一轮
        return ES_TRUE;
    }

    return ES_FALSE;
}

ES_BOOL es_spec_car_get_safetybelt_detect(ES_VOID)
{
    return mf_brd.cfg.safetybelt_detect;
}

ES_S32 es_spec_car_get_limit_speed(ES_VOID)
{
    return mf_brd.cfg.limit_speed;
}

ES_S32 es_spec_car_update_gps_cache_str(const ES_CHAR* gps_str)
{
    strncpy(gps_cache_str, gps_str, LTE_GPS_CACHE_STR_LEN);
    gps_cache_update = ES_TRUE;
    return ES_RET_SUCCESS;
}

ES_S32 es_spec_car_get_gps_from_cache(ES_VOID *gps)
{
    es_gps_info_t *gps_info;

    gps_info = (es_gps_info_t *)gps;
    if (ES_NULL == gps_info) {
        return ES_RET_FAILURE;
    }

    if (0 == gps_cache_str[0]) {
        return ES_RET_FAILURE;
    }

    if (ES_RET_SUCCESS != es_gps_minmea_parse(gps_cache_str, gps_info)) {
        return ES_RET_FAILURE;
    }

    memset(gps_info->spkm, 0X00, sizeof(gps_info->spkm));
    strcpy(gps_info->spkm, "0.0");
    memset(gps_info->spkn, 0X00, sizeof(gps_info->spkn));
    strcpy(gps_info->spkn, "0.0");
    es_log_info("es_spec_car_get_gps_from_cache");
    return ES_RET_SUCCESS;
}

#endif